from authlib.deprecate import deprecate

from .rfc7517 import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def loads(obj, kid=None):
    deprecate("Please use ``<PERSON>sonWeb<PERSON>ey`` directly.")
    key_set = JsonWebKey.import_key_set(obj)
    if key_set:
        return key_set.find_by_kid(kid)
    return Json<PERSON>eb<PERSON><PERSON>.import_key(obj)


def dumps(key, kty=None, **params):
    deprecate("Please use ``<PERSON>sonWeb<PERSON>ey`` directly.")
    if kty:
        params["kty"] = kty

    key = JsonWebKey.import_key(key, params)
    return dict(key)
