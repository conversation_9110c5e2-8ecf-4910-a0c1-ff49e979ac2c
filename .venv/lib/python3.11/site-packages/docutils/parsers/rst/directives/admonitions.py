# $Id: admonitions.py 9881 2024-08-10 08:12:14Z milde $
# Author: <PERSON> <<EMAIL>>
# Copyright: This module has been placed in the public domain.

"""
Admonition directives.
"""

__docformat__ = 'reStructuredText'


from docutils.parsers.rst import Directive
from docutils.parsers.rst import directives
from docutils.parsers.rst.roles import normalize_options
from docutils import nodes


class BaseAdmonition(Directive):

    final_argument_whitespace = True
    option_spec = {'class': directives.class_option,
                   'name': directives.unchanged}
    has_content = True

    node_class = None
    """Subclasses must set this to the appropriate admonition node class."""

    def run(self):
        options = normalize_options(self.options)
        self.assert_has_content()
        text = '\n'.join(self.content)
        admonition_node = self.node_class(text, **options)
        self.add_name(admonition_node)
        admonition_node.source, admonition_node.line = \
            self.state_machine.get_source_and_line(self.lineno)
        if self.node_class is nodes.admonition:
            title_text = self.arguments[0]
            textnodes, messages = self.state.inline_text(title_text,
                                                         self.lineno)
            title = nodes.title(title_text, '', *textnodes)
            title.source, title.line = (
                    self.state_machine.get_source_and_line(self.lineno))
            admonition_node += title
            admonition_node += messages
            if 'classes' not in options:
                admonition_node['classes'] += ['admonition-'
                                               + nodes.make_id(title_text)]
        self.state.nested_parse(self.content, self.content_offset,
                                admonition_node)
        return [admonition_node]


class Admonition(BaseAdmonition):

    required_arguments = 1
    node_class = nodes.admonition


class Attention(BaseAdmonition):

    node_class = nodes.attention


class Caution(BaseAdmonition):

    node_class = nodes.caution


class Danger(BaseAdmonition):

    node_class = nodes.danger


class Error(BaseAdmonition):

    node_class = nodes.error


class Hint(BaseAdmonition):

    node_class = nodes.hint


class Important(BaseAdmonition):

    node_class = nodes.important


class Note(BaseAdmonition):

    node_class = nodes.note


class Tip(BaseAdmonition):

    node_class = nodes.tip


class Warning(BaseAdmonition):  # NoQA: A001 (builtin shadowing)

    node_class = nodes.warning
