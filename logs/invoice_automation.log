2025-08-04 10:21:54,725 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and summarize'
2025-08-04 10:21:54,725 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 10:21:54,744 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:21:54,778 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:21:58,688 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:21:58,693 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:21:58,708 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:21:58,708 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:21:58,722 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:21:58,730 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:21:58,733 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:21:58,749 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:00,362 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:22:00,365 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:22:00,377 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:00,377 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:00,385 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:22:00,393 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:00,396 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:00,405 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:01,422 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:22:01,424 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:22:01,435 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:01,436 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:01,455 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:01,464 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 10:22:01,467 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=a3d0d4ca60e842b7aae40d8882a71dae "HTTP/1.1 202 Accepted"
2025-08-04 10:22:01,468 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:22:01,470 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=a3d0d4ca60e842b7aae40d8882a71dae "HTTP/1.1 202 Accepted"
2025-08-04 10:22:01,471 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=a3d0d4ca60e842b7aae40d8882a71dae "HTTP/1.1 202 Accepted"
2025-08-04 10:22:01,471 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 10:22:01,473 - __main__ - INFO - Initializing browser and page...
2025-08-04 10:22:02,953 - __main__ - INFO - Browser and page initialized.
2025-08-04 10:22:02,953 - __main__ - INFO - 📄 Looking for invoice reference: 1
2025-08-04 10:24:02,975 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=a3d0d4ca60e842b7aae40d8882a71dae "HTTP/1.1 202 Accepted"
2025-08-04 10:24:02,975 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 10:24:02,987 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:24:03,015 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:24:04,473 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:24:04,476 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:24:04,487 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:24:04,488 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:24:04,500 - __main__ - INFO - Workflow completed successfully
2025-08-04 10:24:04,500 - __main__ - INFO - Main workflow completed successfully
2025-08-04 10:24:04,505 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:24:04,515 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:01,383 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1'
2025-08-04 10:35:01,383 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 10:35:01,395 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:35:01,425 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:05,285 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:35:05,289 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:35:05,301 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:05,304 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:05,311 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:35:05,322 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:05,322 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:05,336 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:06,985 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:35:06,987 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:35:07,000 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:07,000 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:07,011 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:35:07,021 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:07,022 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:07,033 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:08,049 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:35:08,051 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:35:08,063 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:08,066 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:08,076 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:08,085 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 10:35:08,087 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:35:08,088 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=b821e63af45545e5a1f425cef984ced4 "HTTP/1.1 202 Accepted"
2025-08-04 10:35:08,090 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=b821e63af45545e5a1f425cef984ced4 "HTTP/1.1 202 Accepted"
2025-08-04 10:35:08,091 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=b821e63af45545e5a1f425cef984ced4 "HTTP/1.1 202 Accepted"
2025-08-04 10:35:08,092 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 10:35:08,093 - __main__ - INFO - Initializing browser and page...
2025-08-04 10:35:09,115 - __main__ - INFO - Browser and page initialized.
2025-08-04 10:35:09,115 - __main__ - INFO - 📄 Looking for invoice reference: 1
2025-08-04 10:36:13,350 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=b821e63af45545e5a1f425cef984ced4 "HTTP/1.1 202 Accepted"
2025-08-04 10:36:13,350 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 10:36:13,358 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:36:13,392 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:36:14,653 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:36:14,656 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:36:14,668 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:36:14,668 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:36:14,677 - __main__ - INFO - Workflow completed successfully
2025-08-04 10:36:14,677 - __main__ - INFO - Main workflow completed successfully
2025-08-04 10:36:14,684 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:36:14,694 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:36:58,631 - __main__ - INFO - Starting Invoice Summarizer MCP server on http://localhost:5001/sse ...
2025-08-04 10:37:19,162 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1'
2025-08-04 10:37:19,162 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 10:37:19,179 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:37:19,213 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:20,377 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:37:20,380 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:37:20,392 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:20,394 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:20,404 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:37:20,407 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:20,414 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:20,418 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:21,529 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:37:21,531 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:37:21,543 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:21,544 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:21,551 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:37:21,560 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:21,562 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:21,571 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:22,688 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:37:22,690 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:37:22,701 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:22,701 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:22,709 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:37:22,716 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:22,721 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:22,727 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:23,871 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:37:23,874 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:37:23,886 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:23,886 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:23,902 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:23,911 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 10:37:23,914 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:23,914 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=d8aba3b75e194439bb4a7dff992f8365 "HTTP/1.1 202 Accepted"
2025-08-04 10:37:23,917 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=d8aba3b75e194439bb4a7dff992f8365 "HTTP/1.1 202 Accepted"
2025-08-04 10:37:23,918 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=d8aba3b75e194439bb4a7dff992f8365 "HTTP/1.1 202 Accepted"
2025-08-04 10:37:23,918 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 10:37:23,919 - __main__ - INFO - Initializing browser and page...
2025-08-04 10:37:25,431 - __main__ - INFO - Browser and page initialized.
2025-08-04 10:37:25,431 - __main__ - INFO - 🔐 Signing in to invoice-generator.com...
2025-08-04 10:37:57,929 - __main__ - INFO - ✅ Sign in successful.
2025-08-04 10:37:57,935 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=d8aba3b75e194439bb4a7dff992f8365 "HTTP/1.1 202 Accepted"
2025-08-04 10:37:57,935 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 10:37:57,944 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:37:57,973 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:59,767 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:37:59,768 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:37:59,780 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:59,780 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:59,792 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 10:37:59,793 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:37:59,795 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=ce42b97776bc4eeea9d00a3e4c6f7aac "HTTP/1.1 202 Accepted"
2025-08-04 10:37:59,797 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=ce42b97776bc4eeea9d00a3e4c6f7aac "HTTP/1.1 202 Accepted"
2025-08-04 10:37:59,798 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=ce42b97776bc4eeea9d00a3e4c6f7aac "HTTP/1.1 202 Accepted"
2025-08-04 10:37:59,798 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 10:37:59,798 - __main__ - INFO - 📄 Looking for invoice reference: 1
2025-08-04 10:37:59,805 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:00,321 - __main__ - INFO - ✅ Invoice reference clicked.
2025-08-04 10:38:00,324 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=ce42b97776bc4eeea9d00a3e4c6f7aac "HTTP/1.1 202 Accepted"
2025-08-04 10:38:00,324 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 10:38:00,330 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:38:00,356 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:01,911 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:38:01,912 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:38:01,925 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:01,925 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:01,936 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 10:38:01,939 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=7c4ec1d51bd64434bd2e16bfac522292 "HTTP/1.1 202 Accepted"
2025-08-04 10:38:01,939 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:01,942 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=7c4ec1d51bd64434bd2e16bfac522292 "HTTP/1.1 202 Accepted"
2025-08-04 10:38:01,943 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=7c4ec1d51bd64434bd2e16bfac522292 "HTTP/1.1 202 Accepted"
2025-08-04 10:38:01,943 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 10:38:01,944 - __main__ - INFO - Downloading invoice PDF...
2025-08-04 10:38:01,963 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:03,043 - __main__ - INFO - ✅ Invoice downloaded to: ../downloaded_invoices/invoice_1.pdf
2025-08-04 10:38:03,046 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=7c4ec1d51bd64434bd2e16bfac522292 "HTTP/1.1 202 Accepted"
2025-08-04 10:38:03,046 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 10:38:03,048 - mcp_tools - INFO - Download result: CallToolResult(content=[TextContent(type='text', text='Invoice downloaded to: ../downloaded_invoices/invoice_1.pdf', annotations=None, meta=None)], structured_content={'result': 'Invoice downloaded to: ../downloaded_invoices/invoice_1.pdf'}, data='Invoice downloaded to: ../downloaded_invoices/invoice_1.pdf', is_error=False)
2025-08-04 10:38:03,048 - mcp_tools - INFO - Tracking downloaded file: ../downloaded_invoices/invoice_1.pdf', annotations=None, meta=None)], structured_content={'result': 'Invoice downloaded to: ../downloaded_invoices/invoice_1.pdf'}, data='Invoice downloaded to: ../downloaded_invoices/invoice_1.pdf', is_error=False)
2025-08-04 10:38:03,052 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:38:03,084 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:05,679 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:38:05,680 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:38:05,692 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:05,693 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:05,700 - __main__ - INFO - Workflow completed successfully
2025-08-04 10:38:05,700 - __main__ - INFO - Main workflow completed successfully
2025-08-04 10:38:05,706 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:38:05,718 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:01,057 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and summarize it'
2025-08-04 10:40:01,058 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 10:40:01,066 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:40:01,094 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:03,914 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:40:03,916 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:40:03,928 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:03,929 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:03,935 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:40:03,946 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:03,946 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:03,959 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:05,423 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:40:05,424 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:40:05,435 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:05,436 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:05,440 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:40:05,451 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:05,451 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:05,463 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:06,577 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:40:06,581 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:40:06,593 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:06,594 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:06,605 - __main__ - INFO - Workflow completed successfully
2025-08-04 10:40:06,605 - __main__ - INFO - Main workflow completed successfully
2025-08-04 10:40:06,611 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:06,622 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:40:41,137 - __main__ - INFO - Starting Invoice Summarizer MCP server on http://localhost:5001/sse ...
2025-08-04 10:41:21,575 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and summarize it'
2025-08-04 10:41:21,576 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 10:41:21,594 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:41:21,623 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:23,735 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:41:23,739 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:41:23,753 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:23,753 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:23,764 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:41:23,771 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:23,775 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:23,783 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:25,945 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:41:25,948 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:41:25,960 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:25,960 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:25,969 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:41:25,977 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:25,980 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:25,989 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:28,111 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:41:28,114 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:41:28,126 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:28,126 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:28,137 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:41:28,146 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:28,149 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:28,162 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:30,313 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:41:30,316 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:41:30,328 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:30,329 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:30,338 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:41:30,343 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:30,349 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:30,354 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:32,534 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:41:32,537 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:41:32,549 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:32,549 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:32,559 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:41:32,567 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:32,569 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:32,579 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:34,782 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:41:34,785 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:41:34,798 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:34,798 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:34,806 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:41:34,816 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:34,817 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:34,828 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:37,058 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:41:37,061 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:41:37,073 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:37,073 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:37,081 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:41:37,088 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:37,092 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:37,099 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:39,474 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:41:39,477 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:41:39,489 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:39,490 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:39,507 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:39,512 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 10:41:39,515 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=37ccb54583484b1aa4b8b606ce6238e2 "HTTP/1.1 202 Accepted"
2025-08-04 10:41:39,516 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=37ccb54583484b1aa4b8b606ce6238e2 "HTTP/1.1 202 Accepted"
2025-08-04 10:41:39,517 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=37ccb54583484b1aa4b8b606ce6238e2 "HTTP/1.1 202 Accepted"
2025-08-04 10:41:39,517 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 10:41:39,518 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:41:39,519 - __main__ - INFO - Initializing browser and page...
2025-08-04 10:41:40,888 - __main__ - INFO - Browser and page initialized.
2025-08-04 10:41:40,888 - __main__ - INFO - 🔐 Signing in to invoice-generator.com...
2025-08-04 10:42:13,589 - __main__ - INFO - ✅ Sign in successful.
2025-08-04 10:42:13,594 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=37ccb54583484b1aa4b8b606ce6238e2 "HTTP/1.1 202 Accepted"
2025-08-04 10:42:13,594 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 10:42:13,604 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:42:13,630 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:16,297 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:42:16,298 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:42:16,309 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:16,310 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:16,320 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:16,321 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 10:42:16,324 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=1435e26885fa45af88b23e899564ff95 "HTTP/1.1 202 Accepted"
2025-08-04 10:42:16,325 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=1435e26885fa45af88b23e899564ff95 "HTTP/1.1 202 Accepted"
2025-08-04 10:42:16,326 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=1435e26885fa45af88b23e899564ff95 "HTTP/1.1 202 Accepted"
2025-08-04 10:42:16,326 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 10:42:16,327 - __main__ - INFO - 🔐 Signing in to invoice-generator.com...
2025-08-04 10:42:16,330 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:46,704 - __main__ - ERROR - Sign in error
Traceback (most recent call last):
  File "/Users/<USER>/playwright_mcp_crewai/mcp_server/playwright_automation_server.py", line 93, in signin_invoice_generator
    await page.click('text="Sign In"')
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 9878, in click
    await self._impl_obj.click(
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_page.py", line 856, in click
    return await self._main_frame.click(**locals_to_params(locals()))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_frame.py", line 549, in click
    await self._channel.send("click", self._timeout, locals_to_params(locals()))
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TimeoutError: Page.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("text=\"Sign In\"")

2025-08-04 10:42:46,709 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=1435e26885fa45af88b23e899564ff95 "HTTP/1.1 202 Accepted"
2025-08-04 10:42:46,709 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 10:42:46,714 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:42:46,746 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:49,377 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:42:49,379 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:42:49,390 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:49,391 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:49,400 - __main__ - INFO - Workflow completed successfully
2025-08-04 10:42:49,400 - __main__ - INFO - Main workflow completed successfully
2025-08-04 10:42:49,408 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:42:49,419 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:26,795 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and email the <NAME_EMAIL>'
2025-08-04 10:43:26,795 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 10:43:26,802 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:26,828 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:29,931 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:29,932 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:29,943 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:29,943 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:29,947 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:29,957 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:29,958 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:29,970 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:31,413 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:31,414 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:31,425 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:31,426 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:31,430 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:31,437 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:31,441 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:31,448 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:32,903 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:32,904 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:32,916 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:32,916 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:32,920 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:32,931 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:32,931 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:32,942 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:34,398 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:34,399 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:34,411 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:34,411 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:34,416 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:34,426 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:34,427 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:34,438 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:35,921 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:35,922 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:35,933 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:35,934 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:35,938 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:35,947 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:35,948 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:35,959 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:37,335 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:37,337 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:37,349 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:37,349 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:37,353 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:37,363 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:37,364 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:37,375 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:38,766 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:38,767 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:38,779 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:38,779 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:38,783 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:38,794 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:38,795 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:38,806 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:40,202 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:40,203 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:40,214 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:40,214 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:40,219 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:40,229 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:40,229 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:40,240 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:41,765 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:41,766 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:41,778 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:41,778 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:41,782 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:41,791 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:41,793 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:41,803 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:43,328 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:43,329 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:43,340 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:43,340 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:43,344 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:43,355 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:43,355 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:43,367 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,285 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:44,286 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:44,297 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,298 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,300 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:43:44,312 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,312 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,325 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,955 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:43:44,956 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:43:44,968 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,968 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,974 - __main__ - INFO - Workflow completed successfully
2025-08-04 10:43:44,974 - __main__ - INFO - Main workflow completed successfully
2025-08-04 10:43:44,982 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:43:44,993 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:45:49,940 - __main__ - INFO - Starting Invoice Summarizer MCP server on http://localhost:5001/sse ...
2025-08-04 10:46:35,615 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and email the <NAME_EMAIL>'
2025-08-04 10:46:35,615 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 10:46:35,626 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:46:35,654 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:37,890 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:46:37,893 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:46:37,905 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:37,906 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:37,915 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:46:37,924 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:37,926 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:37,939 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:39,130 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:46:39,133 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:46:39,145 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:39,145 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:39,154 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 10:46:39,163 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:39,165 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:39,175 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:40,643 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 10:46:40,645 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 10:46:40,657 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:40,657 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:40,668 - __main__ - INFO - Workflow completed successfully
2025-08-04 10:46:40,668 - __main__ - INFO - Main workflow completed successfully
2025-08-04 10:46:40,673 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 10:46:40,684 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:46,082 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and summarize it'
2025-08-04 11:00:46,083 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 11:00:46,099 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:00:46,131 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,387 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:00:48,390 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:00:48,403 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,403 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,416 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:00:48,419 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,427 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,430 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,813 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:00:48,815 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:00:48,827 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,828 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,837 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:00:48,847 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,849 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:48,861 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:49,658 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:00:49,660 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:00:49,672 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:49,672 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:49,689 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:49,699 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 11:00:49,700 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:00:49,703 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=922de40f5a69416fbe050c5cbab3b83a "HTTP/1.1 202 Accepted"
2025-08-04 11:00:49,706 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=922de40f5a69416fbe050c5cbab3b83a "HTTP/1.1 202 Accepted"
2025-08-04 11:00:49,706 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=922de40f5a69416fbe050c5cbab3b83a "HTTP/1.1 202 Accepted"
2025-08-04 11:00:49,707 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 11:00:49,708 - __main__ - INFO - Initializing browser and page...
2025-08-04 11:00:51,203 - __main__ - INFO - Browser and page initialized.
2025-08-04 11:00:51,203 - __main__ - INFO - 🔐 Signing in to invoice-generator.com...
2025-08-04 11:01:23,528 - __main__ - INFO - ✅ Sign in successful.
2025-08-04 11:01:23,534 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=922de40f5a69416fbe050c5cbab3b83a "HTTP/1.1 202 Accepted"
2025-08-04 11:01:23,534 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 11:01:23,544 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:01:23,573 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:24,315 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:01:24,316 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:01:24,328 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:24,328 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:24,340 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 11:01:24,342 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:24,343 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=938de3eaa3a846898dc077e8e0267545 "HTTP/1.1 202 Accepted"
2025-08-04 11:01:24,344 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=938de3eaa3a846898dc077e8e0267545 "HTTP/1.1 202 Accepted"
2025-08-04 11:01:24,345 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=938de3eaa3a846898dc077e8e0267545 "HTTP/1.1 202 Accepted"
2025-08-04 11:01:24,345 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 11:01:24,346 - __main__ - INFO - 📄 Looking for invoice reference: 1
2025-08-04 11:01:24,353 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:24,563 - __main__ - INFO - ✅ Invoice reference clicked.
2025-08-04 11:01:24,566 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=938de3eaa3a846898dc077e8e0267545 "HTTP/1.1 202 Accepted"
2025-08-04 11:01:24,566 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 11:01:24,575 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:01:24,605 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:25,283 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:01:25,284 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:01:25,296 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:25,296 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:25,302 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:01:25,309 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:25,313 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:25,320 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:26,271 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:01:26,275 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:01:26,294 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:26,294 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:26,300 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:01:26,309 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:26,313 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:26,321 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:27,497 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:01:27,499 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:01:27,510 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:27,510 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:27,522 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 11:01:27,523 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:27,524 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=bcc30cedcf334a08a9e53b5b5de9b1ca "HTTP/1.1 202 Accepted"
2025-08-04 11:01:27,526 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=bcc30cedcf334a08a9e53b5b5de9b1ca "HTTP/1.1 202 Accepted"
2025-08-04 11:01:27,527 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=bcc30cedcf334a08a9e53b5b5de9b1ca "HTTP/1.1 202 Accepted"
2025-08-04 11:01:27,527 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 11:01:27,528 - __main__ - INFO - 🔐 Signing in to invoice-generator.com...
2025-08-04 11:01:27,535 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:57,969 - __main__ - ERROR - Sign in error
Traceback (most recent call last):
  File "/Users/<USER>/playwright_mcp_crewai/mcp_server/playwright_automation_server.py", line 93, in signin_invoice_generator
    await page.click('text="Sign In"')
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/async_api/_generated.py", line 9878, in click
    await self._impl_obj.click(
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_page.py", line 856, in click
    return await self._main_frame.click(**locals_to_params(locals()))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_frame.py", line 549, in click
    await self._channel.send("click", self._timeout, locals_to_params(locals()))
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 69, in send
    return await self._connection.wrap_api_call(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_connection.py", line 558, in wrap_api_call
    raise rewrite_error(error, f"{parsed_st['apiName']}: {error}") from None
playwright._impl._errors.TimeoutError: Page.click: Timeout 30000ms exceeded.
Call log:
  - waiting for locator("text=\"Sign In\"")

2025-08-04 11:01:57,973 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=bcc30cedcf334a08a9e53b5b5de9b1ca "HTTP/1.1 202 Accepted"
2025-08-04 11:01:57,973 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 11:01:57,978 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:01:58,002 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:59,248 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:01:59,249 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:01:59,260 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:59,262 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:59,267 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:01:59,274 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:59,278 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:01:59,285 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:00,438 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:02:00,439 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:02:00,451 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:00,452 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:00,458 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:02:00,465 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:00,469 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:00,477 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:01,615 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:02:01,617 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:02:01,628 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:01,628 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:01,636 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:02:01,644 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:01,648 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:01,655 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:02,780 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:02:02,784 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:02:02,795 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:02,796 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:02,801 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:02:02,813 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:02,813 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:02,826 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:03,302 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:02:03,306 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:02:03,318 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:03,318 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:03,324 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:02:03,332 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:03,335 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:03,344 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:04,504 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:02:04,509 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:02:04,522 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:04,523 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:04,529 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:02:04,537 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:04,540 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:04,550 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:05,062 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:02:05,067 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:02:05,079 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:05,081 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:05,097 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 11:02:05,097 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:02:05,100 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=538926d9218e4227854701e7667d09dc "HTTP/1.1 202 Accepted"
2025-08-04 11:02:05,102 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=538926d9218e4227854701e7667d09dc "HTTP/1.1 202 Accepted"
2025-08-04 11:02:05,102 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 11:02:05,103 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=538926d9218e4227854701e7667d09dc "HTTP/1.1 202 Accepted"
2025-08-04 11:02:05,103 - __main__ - INFO - 📄 Looking for invoice reference: Invoice #1
2025-08-04 11:02:05,109 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:05,118 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 11:04:05,118 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=538926d9218e4227854701e7667d09dc "HTTP/1.1 202 Accepted"
2025-08-04 11:04:05,124 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:04:05,154 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:06,422 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:04:06,427 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:04:06,439 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:06,439 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:06,444 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:04:06,455 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:06,455 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:06,467 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:06,982 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:04:06,987 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:04:06,999 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:07,000 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:07,005 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:04:07,014 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:07,017 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:07,026 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:08,231 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:04:08,235 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:04:08,248 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:08,248 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:08,254 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:04:08,266 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:08,266 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:08,279 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:09,671 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:04:09,674 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:04:09,686 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:09,686 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:09,700 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:09,712 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:09,743 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:04:09,754 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:11,773 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:04:11,779 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:04:11,792 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:11,793 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:11,799 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:04:11,811 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:11,811 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:11,823 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:13,193 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:04:13,198 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:04:13,211 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:13,212 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:13,229 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:13,241 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:13,266 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:04:13,279 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:14,745 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:04:14,752 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:04:14,765 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:14,765 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:14,771 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 11:04:14,783 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:14,783 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:14,796 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:15,589 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 11:04:15,594 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 11:04:15,607 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:15,608 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:15,621 - __main__ - INFO - Workflow completed successfully
2025-08-04 11:04:15,621 - __main__ - INFO - Main workflow completed successfully
2025-08-04 11:04:15,628 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 11:04:15,640 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:36:18,312 - __main__ - INFO - Starting Invoice Summarizer MCP server on http://localhost:5001/sse ...
2025-08-04 22:37:25,614 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and summarize'
2025-08-04 22:37:25,614 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 22:37:25,635 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 22:37:25,670 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:27,906 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:37:27,912 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:37:27,926 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:27,927 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:27,934 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 22:37:27,940 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:27,944 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:27,952 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,342 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:37:28,345 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:37:28,357 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,357 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,362 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 22:37:28,373 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,373 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,387 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,777 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:37:28,780 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:37:28,792 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,793 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,798 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 22:37:28,808 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,808 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:28,820 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,212 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:37:29,216 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:37:29,228 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,228 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,235 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.2:latest; provider = ollama
2025-08-04 22:37:29,245 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,246 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,256 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,629 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:37:29,632 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:37:29,645 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,645 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,650 - __main__ - INFO - Workflow completed successfully
2025-08-04 22:37:29,650 - __main__ - INFO - Main workflow completed successfully
2025-08-04 22:37:29,658 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:37:29,670 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:40,809 - __main__ - INFO - Starting CrewAI workflow for: 'dowmload invoice 1 and summarize'
2025-08-04 22:40:40,810 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 22:40:40,832 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:40:40,865 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:49,370 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:40:49,376 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:40:49,398 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:49,399 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:49,409 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:40:49,416 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:49,419 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:49,428 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:50,779 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:40:50,783 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:40:50,796 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:50,796 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:50,805 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:40:50,815 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:50,816 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:50,826 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:52,217 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:40:52,221 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:40:52,234 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:52,235 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:52,241 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:40:52,247 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:52,252 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:52,258 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:53,034 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:40:53,038 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:40:53,050 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:53,050 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:53,058 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:40:53,068 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:53,069 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:53,079 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:54,072 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:40:54,075 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:40:54,088 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:54,089 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:54,108 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:54,129 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:40:54,130 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 22:40:54,134 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=c842099773014e249edd3b7e2b3bdbe4 "HTTP/1.1 202 Accepted"
2025-08-04 22:40:54,137 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=c842099773014e249edd3b7e2b3bdbe4 "HTTP/1.1 202 Accepted"
2025-08-04 22:40:54,138 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=c842099773014e249edd3b7e2b3bdbe4 "HTTP/1.1 202 Accepted"
2025-08-04 22:40:54,138 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 22:40:54,140 - __main__ - INFO - Initializing browser and page...
2025-08-04 22:40:55,800 - __main__ - INFO - Browser and page initialized.
2025-08-04 22:40:55,801 - __main__ - INFO - 📄 Looking for invoice reference: 1
2025-08-04 22:42:55,854 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=c842099773014e249edd3b7e2b3bdbe4 "HTTP/1.1 202 Accepted"
2025-08-04 22:42:55,854 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 22:42:55,867 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:42:55,895 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:57,549 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:42:57,553 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:42:57,565 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:57,566 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:57,572 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:42:57,579 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:57,583 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:57,591 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:58,886 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:42:58,888 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:42:58,901 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:58,902 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:58,913 - __main__ - INFO - Workflow completed successfully
2025-08-04 22:42:58,913 - __main__ - INFO - Main workflow completed successfully
2025-08-04 22:42:58,921 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:42:58,932 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:43:54,029 - __main__ - INFO - Starting Invoice Summarizer MCP server on http://localhost:5001/sse ...
2025-08-04 22:44:29,436 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice and summare'
2025-08-04 22:44:29,436 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 22:44:29,458 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:44:29,489 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:31,669 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:44:31,673 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:44:31,686 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:31,686 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:31,693 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:44:31,702 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:31,703 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:31,713 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:33,118 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:44:33,123 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:44:33,143 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:33,143 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:33,148 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:44:33,157 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:33,158 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:33,170 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:34,417 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:44:34,420 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:44:34,434 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:34,434 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:34,439 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:44:34,447 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:34,450 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:34,458 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:35,630 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:44:35,633 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:44:35,645 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:35,645 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:35,651 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:44:35,662 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:35,663 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:35,674 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:36,749 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:44:36,751 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:44:36,764 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:36,765 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:36,770 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:44:36,780 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:36,781 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:36,790 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:37,475 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:44:37,478 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:44:37,490 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:37,491 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:37,496 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:44:37,504 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:37,506 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:37,515 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:38,592 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:44:38,596 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:44:38,609 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:38,610 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:38,625 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:38,633 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 22:44:38,636 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:44:38,637 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=d1b3284a10a94d23be53f3e37201a038 "HTTP/1.1 202 Accepted"
2025-08-04 22:44:38,640 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=d1b3284a10a94d23be53f3e37201a038 "HTTP/1.1 202 Accepted"
2025-08-04 22:44:38,641 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=d1b3284a10a94d23be53f3e37201a038 "HTTP/1.1 202 Accepted"
2025-08-04 22:44:38,641 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 22:44:38,642 - __main__ - INFO - Initializing browser and page...
2025-08-04 22:44:40,043 - __main__ - INFO - Browser and page initialized.
2025-08-04 22:44:40,043 - __main__ - INFO - 📄 Looking for invoice reference: 2
2025-08-04 22:45:51,160 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=d1b3284a10a94d23be53f3e37201a038 "HTTP/1.1 202 Accepted"
2025-08-04 22:45:51,160 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 22:45:51,169 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:45:51,204 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:52,742 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:45:52,746 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:45:52,758 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:52,759 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:52,775 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:52,786 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:52,829 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:45:52,842 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:54,524 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:45:54,528 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:45:54,539 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:54,540 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:54,555 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:54,565 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:54,603 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:45:54,616 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:55,671 - __main__ - INFO - Starting Invoice Summarizer MCP server on http://localhost:5001/sse ...
2025-08-04 22:45:57,406 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:45:57,409 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:45:57,421 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:57,422 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:57,431 - __main__ - INFO - Workflow completed successfully
2025-08-04 22:45:57,431 - __main__ - INFO - Main workflow completed successfully
2025-08-04 22:45:57,441 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:45:57,452 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:25,860 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and summarize'
2025-08-04 22:46:25,860 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 22:46:25,880 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:25,909 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:28,683 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:28,686 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:28,699 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:28,700 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:28,705 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:28,710 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:28,716 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:28,721 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:30,302 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:30,305 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:30,317 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:30,318 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:30,325 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:30,330 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:30,336 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:30,341 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:31,619 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:31,622 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:31,635 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:31,636 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:31,641 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:31,652 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:31,652 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:31,664 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:32,936 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:32,939 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:32,952 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:32,953 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:32,963 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:32,965 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:32,974 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:32,975 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:34,268 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:34,271 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:34,284 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:34,285 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:34,291 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:34,301 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:34,302 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:34,312 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:35,607 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:35,611 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:35,623 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:35,624 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:35,629 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:35,636 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:35,640 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:35,647 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:36,956 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:36,961 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:36,976 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:36,976 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:36,981 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:36,991 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:36,992 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:37,004 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:38,319 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:38,322 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:38,334 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:38,335 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:38,342 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:38,352 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:38,352 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:38,363 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:39,686 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:39,689 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:39,701 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:39,702 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:39,711 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:39,720 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:39,721 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:39,731 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:41,066 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:41,069 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:41,082 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:41,082 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:41,090 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:41,096 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:41,100 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:41,106 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:42,683 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:42,687 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:42,700 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:42,700 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:42,704 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:46:42,713 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:42,716 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:42,725 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:44,015 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:46:44,019 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:46:44,033 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:44,033 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:44,042 - __main__ - INFO - Workflow completed successfully
2025-08-04 22:46:44,042 - __main__ - INFO - Main workflow completed successfully
2025-08-04 22:46:44,049 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:46:44,060 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:29,954 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1'
2025-08-04 22:49:29,954 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 22:49:29,969 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:49:30,000 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:32,043 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:49:32,046 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:49:32,058 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:32,058 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:32,066 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:49:32,077 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:32,077 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:32,089 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:33,196 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:49:33,199 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:49:33,211 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:33,211 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:33,217 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:49:33,228 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:33,229 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:33,241 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:34,358 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:49:34,360 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:49:34,375 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:34,375 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:34,383 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:49:34,393 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:34,393 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:34,405 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:35,384 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:49:35,388 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:49:35,400 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:35,402 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:35,419 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:35,427 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 22:49:35,430 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=b296047c4a194df686b304dbedc0343c "HTTP/1.1 202 Accepted"
2025-08-04 22:49:35,431 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:49:35,433 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=b296047c4a194df686b304dbedc0343c "HTTP/1.1 202 Accepted"
2025-08-04 22:49:35,434 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=b296047c4a194df686b304dbedc0343c "HTTP/1.1 202 Accepted"
2025-08-04 22:49:35,434 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 22:49:35,435 - __main__ - INFO - Initializing browser and page...
2025-08-04 22:49:36,889 - __main__ - INFO - Browser and page initialized.
2025-08-04 22:49:36,889 - __main__ - INFO - 📄 Looking for invoice reference: 1
2025-08-04 22:51:36,921 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=b296047c4a194df686b304dbedc0343c "HTTP/1.1 202 Accepted"
2025-08-04 22:51:36,921 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 22:51:36,935 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:51:36,964 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:51:38,342 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:51:38,344 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:51:38,356 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:51:38,357 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:51:38,372 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 22:51:38,373 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:51:38,376 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=db46fc9aed44446ca19661960efc28c7 "HTTP/1.1 202 Accepted"
2025-08-04 22:51:38,377 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=db46fc9aed44446ca19661960efc28c7 "HTTP/1.1 202 Accepted"
2025-08-04 22:51:38,378 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=db46fc9aed44446ca19661960efc28c7 "HTTP/1.1 202 Accepted"
2025-08-04 22:51:38,378 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 22:51:38,379 - __main__ - INFO - Downloading invoice PDF...
2025-08-04 22:51:38,384 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:38,431 - __main__ - ERROR - Download error
Traceback (most recent call last):
  File "/Users/<USER>/playwright_mcp_crewai/mcp_server/playwright_automation_server.py", line 160, in download_invoice_pdf
    async with page.expect_download() as download_info:
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_async_base.py", line 60, in __aexit__
    await self._event.value
  File "/Users/<USER>/playwright_mcp_crewai/.venv/lib/python3.11/site-packages/playwright/_impl/_async_base.py", line 35, in value
    return mapping.from_maybe_impl(await self._future)
                                   ^^^^^^^^^^^^^^^^^^
playwright._impl._errors.TimeoutError: Timeout 30000ms exceeded while waiting for event "download"
=========================== logs ===========================
waiting for event "download"
============================================================
2025-08-04 22:53:52,388 - __main__ - INFO - Starting CrewAI workflow for: 'download invoice 1 and summarize'
2025-08-04 22:53:52,388 - __main__ - INFO - Intelligent coordination with 1 agents
2025-08-04 22:53:52,408 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:53:52,439 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:54,427 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:53:54,431 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:53:54,443 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:54,444 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:54,452 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:53:54,461 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:54,463 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:54,472 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:55,803 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:53:55,804 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:53:55,815 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:55,816 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:55,819 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:53:55,829 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:55,829 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:55,841 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:57,214 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:53:57,217 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:53:57,230 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:57,230 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:57,238 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:53:57,248 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:57,249 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:57,259 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:58,264 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:53:58,267 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:53:58,280 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:58,280 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:58,292 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:53:58,299 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:58,303 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:58,311 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:59,269 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:53:59,273 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:53:59,286 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:59,286 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:59,292 - mcp_tools - INFO - Calling tool click_invoice_reference with kwargs: {'reference_text': '1'}
2025-08-04 22:53:59,300 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:59,307 - httpx - INFO - HTTP Request: GET http://localhost:5001/sse/ "HTTP/1.1 200 OK"
2025-08-04 22:53:59,310 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:53:59,311 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=42e75799929c4470b83f2f80031f95f6 "HTTP/1.1 202 Accepted"
2025-08-04 22:53:59,314 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=42e75799929c4470b83f2f80031f95f6 "HTTP/1.1 202 Accepted"
2025-08-04 22:53:59,315 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=42e75799929c4470b83f2f80031f95f6 "HTTP/1.1 202 Accepted"
2025-08-04 22:53:59,315 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-04 22:53:59,316 - __main__ - INFO - 📄 Looking for invoice reference: 1
2025-08-04 22:55:59,338 - httpx - INFO - HTTP Request: POST http://localhost:5001/messages/?session_id=42e75799929c4470b83f2f80031f95f6 "HTTP/1.1 202 Accepted"
2025-08-04 22:55:59,338 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-04 22:55:59,352 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:55:59,382 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:00,868 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:56:00,869 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:56:00,880 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:00,880 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:00,886 - LiteLLM - INFO - 
LiteLLM completion() model= llama3.1:8b; provider = ollama
2025-08-04 22:56:00,893 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:00,897 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:00,905 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:01,918 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/generate "HTTP/1.1 200 OK"
2025-08-04 22:56:01,921 - LiteLLM - INFO - Wrapper: Completed Call, calling success_handler
2025-08-04 22:56:01,936 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:01,936 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:01,944 - __main__ - INFO - Workflow completed successfully
2025-08-04 22:56:01,944 - __main__ - INFO - Main workflow completed successfully
2025-08-04 22:56:01,951 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
2025-08-04 22:56:01,961 - httpx - INFO - HTTP Request: POST http://localhost:11434/api/show "HTTP/1.1 200 OK"
