"""CrewAI tools that wrap MCP server functionality"""
import asyncio
import os
from typing import Any, Dict
from crewai.tools import BaseTool
from fastmcp.client import Client
from pypdf import PdfReader
from dotenv import load_dotenv
from logger_config import setup_logging, get_logger

setup_logging()
logger = get_logger(__name__)

load_dotenv()

# Global state to track downloaded files
_downloaded_files = []

class MCPToolWrapper(BaseTool):
    """Base wrapper for MCP tools"""
    
    def __init__(self, server_url: str, tool_name: str, name: str, description: str):
        super().__init__(name=name, description=description)
        object.__setattr__(self, '_server_url', server_url)
        object.__setattr__(self, '_tool_name', tool_name)
    
    async def _call_mcp_tool(self, **kwargs) -> Any:
        """Call the MCP tool asynchronously"""
        client = Client(self._server_url)
        async with client:
            return await client.call_tool(self._tool_name, kwargs)
    
    def _run(self, **kwargs) -> str:
        """Synchronous wrapper for async MCP call"""
        try:
            # Try to get existing loop
            loop = asyncio.get_running_loop()
            # If we're in an async context, create a task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._call_mcp_tool(**kwargs))
                return str(future.result(timeout=30))
        except RuntimeError:
            # No running loop, safe to create new one
            return str(asyncio.run(self._call_mcp_tool(**kwargs)))

class BrowserLoginTool(MCPToolWrapper):
    def __init__(self):
        super().__init__(
            server_url="http://localhost:5001/sse/",
            tool_name="signin_invoice_generator",
            name="browser_login",
            description="Login to invoice generator website using configured environment credentials"
        )
    
    def _run(self) -> str:
        # Always use environment variables, ignore any passed parameters
        email = os.getenv('INVOICE_EMAIL')
        password = os.getenv('INVOICE_PASSWORD')
        
        if not email or not password:
            return "Error: INVOICE_EMAIL and INVOICE_PASSWORD must be set in environment"
        
        try:
            # Try to get existing loop
            loop = asyncio.get_running_loop()
            # If we're in an async context, create a task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._call_mcp_tool(email=email, password=password))
                result = future.result(timeout=30)
        except RuntimeError:
            # No running loop, safe to create new one
            result = asyncio.run(self._call_mcp_tool(email=email, password=password))
        
        return f"Login result: {result}"

class ClickInvoiceReferenceTool(MCPToolWrapper):
    def __init__(self):
        super().__init__(
            server_url="http://localhost:5001/sse/",
            tool_name="click_invoice_reference",
            name="click_invoice_reference",
            description="Click on an invoice reference to view invoice details. Use reference_text='2' for Invoice #2"
        )
    
    def _run(self, reference_text: str = "") -> str:
        try:
            # Try to get existing loop
            loop = asyncio.get_running_loop()
            # If we're in an async context, create a task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._call_mcp_tool(reference_text=reference_text))
                result = future.result(timeout=30)
        except RuntimeError:
            # No running loop, safe to create new one
            result = asyncio.run(self._call_mcp_tool(reference_text=reference_text))
        
        return f"Clicked invoice reference: {result}"

class InvoiceDownloadTool(MCPToolWrapper):
    def __init__(self):
        super().__init__(
            server_url="http://localhost:5001/sse/",
            tool_name="download_invoice_pdf",
            name="download_invoice_pdf",
            description="Download invoice PDF from the website"
        )
    
    def _run(self, filename: str = "downloaded_invoice") -> str:
        global _downloaded_files
        try:
            # Try to get existing loop
            loop = asyncio.get_running_loop()
            # If we're in an async context, create a task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._call_mcp_tool(filename=filename))
                result = future.result(timeout=30)
        except RuntimeError:
            # No running loop, safe to create new one
            result = asyncio.run(self._call_mcp_tool(filename=filename))
        
        # Extract file path from result and track it
        result_str = str(result)
        logger.info(f"Download result: {result_str}")
        if "downloaded to:" in result_str.lower():
            import re
            path_match = re.search(r'downloaded to:\s*([^\n]+)', result_str, re.IGNORECASE)
            if path_match:
                file_path = path_match.group(1).strip()
                logger.info(f"Tracking downloaded file: {file_path}")
                _downloaded_files.append(file_path)
        else:
            logger.warning("No 'downloaded to:' found in result")
        
        return f"Download result: {result}"

class BrowserCloseTool(MCPToolWrapper):
    def __init__(self):
        super().__init__(
            server_url="http://localhost:5001/sse/",
            tool_name="browser_close",
            name="close_browser",
            description="Close the browser session"
        )
    
    def _run(self) -> str:
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(self._call_mcp_tool())
        return f"Browser closed: {result}"

class MultipleInvoiceDownloadTool(MCPToolWrapper):
    def __init__(self):
        super().__init__(
            server_url="http://localhost:5001/sse/",
            tool_name="download_multiple_invoices",
            name="download_multiple_invoices",
            description="Download multiple invoices by their numbers"
        )
    
    def _run(self, invoice_numbers: list, base_filename: str = "invoice") -> str:
        global _downloaded_files
        try:
            # Try to get existing loop
            loop = asyncio.get_running_loop()
            # If we're in an async context, create a task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._call_mcp_tool(invoice_numbers=invoice_numbers, base_filename=base_filename))
                result = future.result(timeout=60)
        except RuntimeError:
            # No running loop, safe to create new one
            result = asyncio.run(self._call_mcp_tool(invoice_numbers=invoice_numbers, base_filename=base_filename))
        
        # Track downloaded files from batch result
        result_str = str(result)
        logger.info(f"Batch download result: {result_str}")
        import re
        # Extract clean file paths from result
        if hasattr(result, 'data'):
            data_str = str(result.data)
            file_matches = re.findall(r'Downloaded to ([^\n\\]+\.pdf)', data_str)
            for file_path in file_matches:
                clean_path = file_path.strip()
                logger.info(f"Tracking batch downloaded file: {clean_path}")
                _downloaded_files.append(clean_path)
        
        return f"Batch download result: {result}"

class DocumentSummarizerTool(MCPToolWrapper):
    def __init__(self):
        super().__init__(
            server_url="http://localhost:5002/sse/",
            tool_name="summarize",
            name="summarize_document",
            description="Summarize document content using AI"
        )
    
    def _run(self, documents: list = None) -> str:
        if not documents:
            # Read the latest downloaded PDF if no documents provided
            logger.info("No documents provided, reading latest downloaded PDF")
            downloads_dir = "downloaded_invoices"
            if os.path.exists(downloads_dir):
                pdf_files = [f for f in os.listdir(downloads_dir) if f.endswith('.pdf')]
                if pdf_files:
                    latest_file = max(pdf_files, key=lambda f: os.path.getctime(os.path.join(downloads_dir, f)))
                    document_path = os.path.join(downloads_dir, latest_file)
                    
                    # Extract text from PDF
                    try:
                        from pypdf import PdfReader
                        reader = PdfReader(document_path)
                        text = ""
                        for page in reader.pages:
                            text += page.extract_text() + "\n"
                        documents = [text.strip()]
                    except Exception as e:
                        return f"Error reading PDF: {e}"
                else:
                    return "No PDF files found to summarize"
            else:
                return "Downloads directory not found"
        
        try:
            # Try to get existing loop
            loop = asyncio.get_running_loop()
            # If we're in an async context, create a task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._call_mcp_tool(documents=documents))
                result = future.result(timeout=30)
        except RuntimeError:
            # No running loop, safe to create new one
            result = asyncio.run(self._call_mcp_tool(documents=documents))
        
        # Extract clean summary text
        if hasattr(result, 'content') and result.content:
            if isinstance(result.content, list) and len(result.content) > 0:
                print(f"Result content: {result.content}")
                return result.content[0].text if hasattr(result.content[0], 'text') else str(result.content[0])
        elif hasattr(result, 'structured_content') and 'result' in result.structured_content:
            print(f"Structured content: {result.structured_content}")
            return result.structured_content['result']
        elif hasattr(result, 'data'):
            return result.data
        else:
            return str(result)

class MultipleInvoiceSummarizerTool(MCPToolWrapper):
    def __init__(self):
        super().__init__(
            server_url="http://localhost:5002/sse/",
            tool_name="summarize_multiple_invoices",
            name="summarize_multiple_invoices",
            description="Summarize multiple invoice files by their filenames"
        )
    
    def _run(self, filenames: str) -> str:
        # Convert to list if needed
        if isinstance(filenames, str):
            if ',' in filenames:
                filenames_list = [f.strip() for f in filenames.split(',')]
            else:
                filenames_list = [filenames.strip()]
        else:
            filenames_list = filenames
            
        try:
            # Try to get existing loop
            loop = asyncio.get_running_loop()
            # If we're in an async context, create a task
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(asyncio.run, self._call_mcp_tool(filenames=filenames_list))
                result = future.result(timeout=60)
        except RuntimeError:
            # No running loop, safe to create new one
            result = asyncio.run(self._call_mcp_tool(filenames=filenames_list))
        
        # Extract clean summary text with debugging
        logger.info(f"Raw result type: {type(result)}")
        logger.info(f"Raw result: {result}")
        logger.debug(f"Result attributes: {dir(result)}")
        
        if hasattr(result, 'content') and result.content:
            logger.info(f"Found content: {result.content}")
            if isinstance(result.content, list) and len(result.content) > 0:
                content = result.content[0].text if hasattr(result.content[0], 'text') else str(result.content[0])
                logger.info(f"Extracted content: {content}")
                return content
        elif hasattr(result, 'structured_content'):
            logger.info(f"Found structured_content: {result.structured_content}")
            if 'result' in result.structured_content:
                logger.info(f"Extracted from structured_content: {result.structured_content['result']}")
                return result.structured_content['result']
        elif hasattr(result, 'data'):
            logger.info(f"Found data: {result.data}")
            return str(result.data)
        else:
            logger.info(f"Fallback to string: {str(result)}")
            return str(result)
